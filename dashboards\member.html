<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="sidebar-brand">ADAMS</div>
                <div class="sidebar-subtitle">Notre Dame of Midsayap College</div>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-role">Member</div>
                <div class="user-name" id="userName">member</div>
            </div>
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="upload">
                        <i class="fas fa-upload nav-icon"></i>
                        <span class="nav-text">Document Upload</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="documents">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Document Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="tracking">
                        <i class="fas fa-search nav-icon"></i>
                        <span class="nav-text">Status Tracking</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="requests">
                        <i class="fas fa-key nav-icon"></i>
                        <span class="nav-text">Access Requests</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="calendar">
                        <i class="fas fa-calendar nav-icon"></i>
                        <span class="nav-text">Deadline Calendar</span>
                    </a>
                </div>
                <hr class="my-3 mx-3" style="border-color: rgba(255,255,255,0.2);">
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </div>
        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="d-flex align-items-center">
                    <button class="sidebar-toggle me-3" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="sidebar-toggle d-lg-none" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title mb-0" id="pageTitle">Dashboard</h1>
                </div>
                <div class="top-nav-actions">
                    <div class="notification-badge me-3">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="badge">2</span>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <span id="topUserName">member</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- Content Area -->
            <div class="content-area" id="contentArea">
                <!-- Dashboard content will be loaded here -->
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- <script src="../js/auth.js"></script> -->
    <!-- <script src="../js/utils.js"></script> -->
    <!-- <script src="../js/dashboard.js"></script> -->
    <script>
        class MemberDashboard extends ADAMSDashboard {
            constructor() {
                super();
                this.setupMemberFeatures();
            }
            setupMemberFeatures() {
                this.loadUserData();
            }
            loadUserData() {
                const user = ADAMSAuth.getCurrentUser();
                if (user) {
                    document.getElementById('userName').textContent = user.username;
                    document.getElementById('topUserName').textContent = user.username;
                }
            }
            renderDocumentUpload(container) {
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Document Upload</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">Upload Documents</h5></div><div class="card-body">
                        <div class="upload-area border-2 border-dashed border-secondary rounded p-5 text-center" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>Drag and drop files here</h5>
                            <p class="text-muted">or click to browse</p>
                            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx" style="display: none;">
                            <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">Choose Files</button>
                        </div>
                        <div id="uploadProgress" class="mt-3" style="display: none;">
                            <div class="progress mb-2"><div class="progress-bar" role="progressbar" style="width: 0%"></div></div>
                            <small class="text-muted">Uploading...</small>
                        </div>
                        <div id="uploadedFiles" class="mt-3"></div>
                    </div></div>
                `;
                this.setupUploadEventListeners();
            }
            renderDocumentManagement(container) {
                const docs = [
                    { id: 1, title: 'Faculty Credentials', type: 'Faculty', status: 'approved', tags: ['credentials', 'faculty'], uploadedAt: new Date(Date.now() - 86400000), fileName: 'faculty_credentials.pdf' },
                    { id: 2, title: 'Research Proposal', type: 'Research', status: 'pending', tags: ['research', 'proposal'], uploadedAt: new Date(Date.now() - 172800000), fileName: 'research_proposal.pdf' },
                    { id: 3, title: 'Student Records', type: 'Student', status: 'draft', tags: ['student', 'records'], uploadedAt: new Date(Date.now() - 259200000), fileName: 'student_records.pdf' }
                ];
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Document Management</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">My Documents</h5></div><div class="card-body">
                        <div class="table-responsive"><table class="table table-hover"><thead><tr><th>Document</th><th>Type</th><th>Tags</th><th>Status</th><th>Uploaded</th><th>Actions</th></tr></thead><tbody>
                        ${docs.map(doc => `
                            <tr>
                                <td><i class="fas ${ADAMSUtils.getFileIcon(doc.fileName)} fa-2x text-muted me-2"></i>${doc.title}</td>
                                <td>${doc.type}</td>
                                <td>${doc.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}</td>
                                <td>${ADAMSUtils.getStatusBadge(doc.status)}</td>
                                <td>${ADAMSUtils.timeAgo(doc.uploadedAt)}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-view" title="View"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-outline-secondary btn-edit" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-outline-danger btn-delete" title="Delete"><i class="fas fa-trash"></i></button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                        </tbody></table></div></div></div>
                `;
            }
            renderStatusTracking(container) {
                const submissions = [
                    { id: 1, title: 'Faculty Credentials', status: 'approved', submittedAt: new Date(Date.now() - 86400000), reviewer: 'Area Chair', comment: 'All requirements met' },
                    { id: 2, title: 'Research Proposal', status: 'pending', submittedAt: new Date(Date.now() - 172800000), reviewer: 'Sub-Area Chair', comment: 'Under review' },
                    { id: 3, title: 'Student Records', status: 'draft', submittedAt: new Date(Date.now() - 259200000), reviewer: 'None', comment: 'Not submitted yet' }
                ];
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Status Tracking</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">Submission Progress</h5></div><div class="card-body">
                        ${submissions.map(sub => `
                            <div class="submission-item border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${sub.title}</h6>
                                        <p class="text-muted mb-1">Submitted: ${ADAMSUtils.timeAgo(sub.submittedAt)}</p>
                                        <p class="text-muted mb-1">Reviewer: ${sub.reviewer}</p>
                                        <p class="text-muted mb-0">Comment: ${sub.comment}</p>
                                    </div>
                                    <div>${ADAMSUtils.getStatusBadge(sub.status)}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div></div>
                `;
            }
            renderAccessRequests(container) {
                const requests = [
                    { id: 1, document: 'Research Data 2024', reason: 'Need access for analysis', status: 'pending', requestedAt: new Date(Date.now() - 86400000) },
                    { id: 2, document: 'Faculty Records', reason: 'Required for accreditation', status: 'approved', requestedAt: new Date(Date.now() - 172800000) },
                    { id: 3, document: 'Student Analytics', reason: 'Research purposes', status: 'rejected', requestedAt: new Date(Date.now() - 259200000) }
                ];
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Access Requests</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">My Access Requests</h5></div><div class="card-body">
                        <div class="table-responsive"><table class="table table-hover"><thead><tr><th>Document</th><th>Reason</th><th>Status</th><th>Requested</th><th>Actions</th></tr></thead><tbody>
                        ${requests.map(req => `
                            <tr>
                                <td>${req.document}</td>
                                <td>${req.reason}</td>
                                <td>${ADAMSUtils.getStatusBadge(req.status)}</td>
                                <td>${ADAMSUtils.timeAgo(req.requestedAt)}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-view" title="View"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-outline-danger btn-cancel" title="Cancel" ${req.status !== 'pending' ? 'disabled' : ''}><i class="fas fa-times"></i></button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                        </tbody></table></div></div></div>
                `;
            }
            renderCalendar(container) {
                const deadlines = [
                    { title: 'Document Submission', date: '2024-12-20', type: 'submission' },
                    { title: 'Review Deadline', date: '2024-12-25', type: 'review' },
                    { title: 'Final Approval', date: '2024-12-30', type: 'approval' }
                ];
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Deadline Calendar</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">Important Deadlines</h5></div><div class="card-body">
                        ${deadlines.map(deadline => `
                            <div class="deadline-item border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">${deadline.title}</h6>
                                        <p class="text-muted mb-0">${deadline.date}</p>
                                    </div>
                                    <span class="badge bg-${this.getDeadlineTypeColor(deadline.type)}">${deadline.type}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div></div>
                `;
            }
            setupUploadEventListeners() {
                const uploadArea = document.getElementById('uploadArea');
                const fileInput = document.getElementById('fileInput');
                const uploadProgress = document.getElementById('uploadProgress');
                const uploadedFiles = document.getElementById('uploadedFiles');
                if (uploadArea && fileInput) {
                    uploadArea.addEventListener('click', () => fileInput.click());
                    uploadArea.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        uploadArea.classList.add('border-primary');
                    });
                    uploadArea.addEventListener('dragleave', () => {
                        uploadArea.classList.remove('border-primary');
                    });
                    uploadArea.addEventListener('drop', (e) => {
                        e.preventDefault();
                        uploadArea.classList.remove('border-primary');
                        const files = e.dataTransfer.files;
                        this.handleFileUpload(files);
                    });
                    fileInput.addEventListener('change', (e) => {
                        this.handleFileUpload(e.target.files);
                    });
                }
            }
            handleFileUpload(files) {
                if (files.length === 0) return;
                const uploadProgress = document.getElementById('uploadProgress');
                const uploadedFiles = document.getElementById('uploadedFiles');
                uploadProgress.style.display = 'block';
                const progressBar = uploadProgress.querySelector('.progress-bar');
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    progressBar.style.width = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                        uploadProgress.style.display = 'none';
                        ADAMSUtils.showToast('Files uploaded successfully', 'success');
                        this.displayUploadedFiles(files);
                    }
                }, 200);
            }
            displayUploadedFiles(files) {
                const uploadedFiles = document.getElementById('uploadedFiles');
                const fileList = Array.from(files).map(file => `
                    <div class="alert alert-success d-flex align-items-center">
                        <i class="fas fa-check-circle me-2"></i>
                        <div>
                            <strong>${file.name}</strong> uploaded successfully
                            <br><small class="text-muted">${ADAMSUtils.formatFileSize(file.size)}</small>
                        </div>
                    </div>
                `).join('');
                uploadedFiles.innerHTML = fileList;
            }
            getDeadlineTypeColor(type) {
                const colors = { submission: 'danger', review: 'warning', approval: 'success' };
                return colors[type] || 'secondary';
            }
        }
        document.addEventListener('DOMContentLoaded', () => {
            new MemberDashboard();
        });
    </script>
    <style>
        .upload-area {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            background-color: var(--gray-light);
        }
        .upload-area.border-primary {
            border-color: var(--ndmc-green) !important;
        }
    </style>
</body>
</html> 