/* ADAMS - Notre Dame of Midsayap College Theme */
:root {
    --ndmc-green: #2E7D32;
    --ndmc-green-light: #4CAF50;
    --ndmc-green-dark: #1B5E20;
    --ndmc-green-lighter: #C8E6C9;
    --ndmc-green-darker: #0D4F14;
    --success-light: #E8F5E8;
    --warning-light: #FFF8E1;
    --danger-light: #FFEBEE;
    --info-light: #E3F2FD;
    --gray-light: #F8F9FA;
    --gray-medium: #6C757D;
    --gray-dark: #343A40;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--gray-dark);
}

/* Login Page Styles */
.login-body {
    background: linear-gradient(135deg, var(--ndmc-green) 0%, var(--ndmc-green-dark) 100%);
    min-height: 100vh;
}

.login-branding {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.login-container {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 450px;
}

.login-container h2 {
    color: var(--ndmc-green);
}

.btn-success {
    background-color: var(--ndmc-green);
    border-color: var(--ndmc-green);
    transition: all 0.3s ease;
}

.btn-success:hover {
    background-color: var(--ndmc-green-dark);
    border-color: var(--ndmc-green-dark);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--ndmc-green);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--ndmc-green) 0%, var(--ndmc-green-dark) 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: all 0.3s ease;
    z-index: 1000;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-logo {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.sidebar-brand {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.sidebar-subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
}

.user-info {
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    margin: 1rem;
    border-radius: 10px;
    text-align: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
}

.user-role {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.user-name {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Navigation Menu */
.nav-menu {
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
}

.nav-icon {
    width: 20px;
    margin-right: 12px;
    text-align: center;
}

.nav-text {
    flex: 1;
}

/* Main Content Area */
.main-content {
    flex: 1;
    margin-left: 280px;
    background: var(--gray-light);
    min-height: 100vh;
    transition: all 0.3s ease;
}

.main-content.expanded {
    margin-left: 70px;
}

/* Top Navigation */
.top-nav {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #E9ECEF;
    display: flex;
    justify-content: between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--gray-medium);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: var(--gray-light);
    color: var(--ndmc-green);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-dark);
    margin: 0;
}

.top-nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-badge {
    position: relative;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #DC3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Content Area */
.content-area {
    padding: 2rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    transition: all 0.3s ease;
    background: white;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: white;
    border-bottom: 1px solid #E9ECEF;
    padding: 1.25rem 1.5rem;
    border-radius: 12px 12px 0 0;
    font-weight: 600;
    color: var(--gray-dark);
}

.card-body {
    padding: 1.5rem;
}

/* Status Indicators */
.status-approved {
    background: var(--success-light);
    color: var(--ndmc-green-dark);
    border: 1px solid var(--ndmc-green-lighter);
}

.status-pending {
    background: var(--warning-light);
    color: #F57C00;
    border: 1px solid #FFE082;
}

.status-rejected {
    background: var(--danger-light);
    color: #D32F2F;
    border: 1px solid #FFCDD2;
}

.status-draft {
    background: var(--info-light);
    color: #1976D2;
    border: 1px solid #BBDEFB;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--ndmc-green);
    border-color: var(--ndmc-green);
}

.btn-primary:hover {
    background-color: var(--ndmc-green-dark);
    border-color: var(--ndmc-green-dark);
}

.btn-outline-success {
    color: var(--ndmc-green);
    border-color: var(--ndmc-green);
}

.btn-outline-success:hover {
    background-color: var(--ndmc-green);
    border-color: var(--ndmc-green);
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: var(--gray-light);
    border: none;
    font-weight: 600;
    color: var(--gray-dark);
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #E9ECEF;
}

/* Forms */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #DEE2E6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--ndmc-green);
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.35rem 0.65rem;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background: #E9ECEF;
}

.progress-bar {
    background: var(--ndmc-green);
    border-radius: 4px;
}

/* Charts and Metrics */
.metric-card {
    background: linear-gradient(135deg, var(--ndmc-green) 0%, var(--ndmc-green-dark) 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.metric-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    opacity: 0.3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .login-container {
        padding: 2rem;
        margin: 1rem;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .top-nav {
        padding: 1rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Modal Styles */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #E9ECEF;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #E9ECEF;
    padding: 1.5rem;
}

/* Dropdown Styles */
.dropdown-menu {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--gray-light);
}

/* Tooltip Styles */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background: var(--gray-dark);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
}

/* Print Styles */
@media print {
    .sidebar,
    .top-nav,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-area {
        padding: 0 !important;
    }
} 