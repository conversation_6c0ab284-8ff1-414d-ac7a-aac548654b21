<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="sidebar-brand">ADAMS</div>
                <div class="sidebar-subtitle">Notre Dame of Midsayap College</div>
            </div>
            
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-role">System Administrator</div>
                <div class="user-name" id="userName">admin</div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="users">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">User Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="config">
                        <i class="fas fa-cogs nav-icon"></i>
                        <span class="nav-text">System Configuration</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="scheduling">
                        <i class="fas fa-calendar-alt nav-icon"></i>
                        <span class="nav-text">Accreditation Scheduling</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="documents">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Document Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="logs">
                        <i class="fas fa-history nav-icon"></i>
                        <span class="nav-text">Audit Logs</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="reports">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        <span class="nav-text">Reports & Analytics</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="announcements">
                        <i class="fas fa-bullhorn nav-icon"></i>
                        <span class="nav-text">Announcements</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="settings">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </div>
                
                <hr class="my-3 mx-3" style="border-color: rgba(255,255,255,0.2);">
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="d-flex align-items-center">
                    <button class="sidebar-toggle me-3" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="sidebar-toggle d-lg-none" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title mb-0" id="pageTitle">Dashboard</h1>
                </div>
                
                <div class="top-nav-actions">
                    <div class="notification-badge me-3">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <span id="topUserName">admin</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="content-area" id="contentArea">
                <!-- Dashboard content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/dashboard.js"></script>
    <script>
        // Admin-specific dashboard functionality
        class AdminDashboard extends ADAMSDashboard {
            constructor() {
                super();
                this.setupAdminFeatures();
            }
            
            setupAdminFeatures() {
                // Additional admin-specific setup
                this.loadUserData();
            }
            
            loadUserData() {
                const user = ADAMSAuth.getCurrentUser();
                if (user) {
                    document.getElementById('userName').textContent = user.username;
                    document.getElementById('topUserName').textContent = user.username;
                }
            }
            
            renderUserManagement(container) {
                const users = ADAMSUtils.generateMockUsers(15);
                
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <h2>User Management</h2>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                    <i class="fas fa-plus me-2"></i>Add User
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">System Users</h5>
                                </div>
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" placeholder="Search users..." id="userSearch">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Department</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${users.map(user => `
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar me-3">
                                                            <i class="fas fa-user-circle fa-2x text-muted"></i>
                                                        </div>
                                                        <div>
                                                            <div class="fw-semibold">${user.name}</div>
                                                            <small class="text-muted">@${user.username}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>${user.email}</td>
                                                <td><span class="badge bg-${this.getRoleColor(user.role)}">${user.role}</span></td>
                                                <td>${user.department}</td>
                                                <td>
                                                    <span class="badge bg-${user.status === 'active' ? 'success' : 'danger'}">
                                                        ${user.status}
                                                    </span>
                                                </td>
                                                <td>${ADAMSUtils.formatDate(user.createdAt)}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary btn-view" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-secondary btn-edit" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger btn-delete" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderSystemConfig(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>System Configuration</h2>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Agencies</h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">CHED</h6>
                                                <small class="text-muted">Commission on Higher Education</small>
                                            </div>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">AACCUP</h6>
                                                <small class="text-muted">Accrediting Agency of Chartered Colleges and Universities</small>
                                            </div>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Accreditation Levels</h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Level I</h6>
                                                <small class="text-muted">Candidate Status</small>
                                            </div>
                                            <span class="badge bg-warning">Pending</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Level II</h6>
                                                <small class="text-muted">Re-accredited Status</small>
                                            </div>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Level III</h6>
                                                <small class="text-muted">Re-accredited Status</small>
                                            </div>
                                            <span class="badge bg-info">In Progress</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderScheduling(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Accreditation Scheduling</h2>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Timeline</h5>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-success"></div>
                                            <div class="timeline-content">
                                                <h6>Document Preparation</h6>
                                                <p class="text-muted">Complete all required documents</p>
                                                <small class="text-success">Completed - Dec 15, 2024</small>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-warning"></div>
                                            <div class="timeline-content">
                                                <h6>Internal Review</h6>
                                                <p class="text-muted">Area chairs review documents</p>
                                                <small class="text-warning">In Progress - Due Jan 15, 2025</small>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-secondary"></div>
                                            <div class="timeline-content">
                                                <h6>External Review</h6>
                                                <p class="text-muted">External evaluators review</p>
                                                <small class="text-muted">Scheduled - Feb 1, 2025</small>
                                            </div>
                                        </div>
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-secondary"></div>
                                            <div class="timeline-content">
                                                <h6>Site Visit</h6>
                                                <p class="text-muted">On-site evaluation</p>
                                                <small class="text-muted">Scheduled - Mar 15, 2025</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Deadlines</h5>
                                </div>
                                <div class="card-body">
                                    <div class="deadline-list">
                                        <div class="deadline-item mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>Document Submission</span>
                                                <span class="text-danger">3 days</span>
                                            </div>
                                            <div class="progress mt-2">
                                                <div class="progress-bar bg-danger" style="width: 85%"></div>
                                            </div>
                                        </div>
                                        <div class="deadline-item mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>Internal Review</span>
                                                <span class="text-warning">15 days</span>
                                            </div>
                                            <div class="progress mt-2">
                                                <div class="progress-bar bg-warning" style="width: 60%"></div>
                                            </div>
                                        </div>
                                        <div class="deadline-item mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>External Review</span>
                                                <span class="text-info">30 days</span>
                                            </div>
                                            <div class="progress mt-2">
                                                <div class="progress-bar bg-info" style="width: 20%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderAuditLogs(container) {
                const logs = [
                    { action: 'User Login', user: 'admin', timestamp: new Date(), ip: '*************', status: 'success' },
                    { action: 'Document Upload', user: 'member1', timestamp: new Date(Date.now() - 60000), ip: '*************', status: 'success' },
                    { action: 'Document Review', user: 'areachair1', timestamp: new Date(Date.now() - 120000), ip: '*************', status: 'success' },
                    { action: 'User Creation', user: 'admin', timestamp: new Date(Date.now() - 180000), ip: '*************', status: 'success' },
                    { action: 'Failed Login', user: 'unknown', timestamp: new Date(Date.now() - 240000), ip: '*************', status: 'failed' }
                ];
                
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Audit Logs</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="mb-0">System Activity</h5>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex gap-2">
                                        <select class="form-select" id="logFilter">
                                            <option value="">All Actions</option>
                                            <option value="login">Login</option>
                                            <option value="upload">Upload</option>
                                            <option value="review">Review</option>
                                        </select>
                                        <button class="btn btn-outline-secondary" onclick="ADAMSUtils.exportToCSV(logs, 'audit_logs.csv')">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Action</th>
                                            <th>User</th>
                                            <th>Timestamp</th>
                                            <th>IP Address</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${logs.map(log => `
                                            <tr>
                                                <td>${log.action}</td>
                                                <td>${log.user}</td>
                                                <td>${ADAMSUtils.formatDateTime(log.timestamp)}</td>
                                                <td>${log.ip}</td>
                                                <td>
                                                    <span class="badge bg-${log.status === 'success' ? 'success' : 'danger'}">
                                                        ${log.status}
                                                    </span>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderReports(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Reports & Analytics</h2>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Document Status Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="statusChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Activity Timeline</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="activityChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Report Generation</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card border">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                                                    <h6>Accreditation Report</h6>
                                                    <p class="text-muted">Generate comprehensive accreditation report</p>
                                                    <button class="btn btn-outline-danger">Generate PDF</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                                                    <h6>Data Export</h6>
                                                    <p class="text-muted">Export data to Excel format</p>
                                                    <button class="btn btn-outline-success">Export Excel</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-chart-bar fa-3x text-primary mb-3"></i>
                                                    <h6>Analytics Report</h6>
                                                    <p class="text-muted">Generate analytics and insights</p>
                                                    <button class="btn btn-outline-primary">Generate Report</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderAnnouncements(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <h2>Announcements</h2>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAnnouncementModal">
                                    <i class="fas fa-plus me-2"></i>New Announcement
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="announcement-list">
                                        <div class="announcement-item border-bottom pb-3 mb-3">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h5 class="mb-1">System Maintenance</h5>
                                                    <p class="text-muted mb-2">Scheduled maintenance on December 20, 2024 from 10:00 PM to 2:00 AM.</p>
                                                    <small class="text-muted">Posted by Admin on Dec 15, 2024</small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-secondary btn-edit">Edit</button>
                                                    <button class="btn btn-outline-danger btn-delete">Delete</button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="announcement-item border-bottom pb-3 mb-3">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h5 class="mb-1">Document Submission Deadline</h5>
                                                    <p class="text-muted mb-2">All documents must be submitted by January 15, 2025 for the upcoming accreditation.</p>
                                                    <small class="text-muted">Posted by Admin on Dec 10, 2024</small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-secondary btn-edit">Edit</button>
                                                    <button class="btn btn-outline-danger btn-delete">Delete</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderSettings(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>System Settings</h2>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Settings</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">Session Timeout (minutes)</label>
                                            <input type="number" class="form-control" value="30">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Password Policy</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Require uppercase letters</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Require numbers</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox">
                                                <label class="form-check-label">Require special characters</label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Save Settings</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notification Settings</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">Email Notifications</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Document submissions</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Review requests</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox">
                                                <label class="form-check-label">System alerts</label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">In-app Notifications</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Enable notifications</label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Save Settings</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            getRoleColor(role) {
                const colors = {
                    admin: 'danger',
                    areachair: 'primary',
                    subareachair: 'info',
                    member: 'secondary'
                };
                return colors[role] || 'secondary';
            }
        }
        
        // Initialize admin dashboard
        document.addEventListener('DOMContentLoaded', () => {
            new AdminDashboard();
        });
    </script>
    
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #dee2e6;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: -29px;
            top: 12px;
            width: 2px;
            height: calc(100% + 18px);
            background: #dee2e6;
        }
        
        .deadline-item {
            padding: 10px 0;
        }
        
        .announcement-item:last-child {
            border-bottom: none !important;
        }
    </style>
</body>
</html> 