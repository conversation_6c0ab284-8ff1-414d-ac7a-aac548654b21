<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sub-Area Chair Dashboard - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="sidebar-brand">ADAMS</div>
                <div class="sidebar-subtitle">Notre Dame of Midsayap College</div>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-role">Sub-Area Chair</div>
                <div class="user-name" id="userName">subareachair</div>
            </div>
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="recommendations">
                        <i class="fas fa-thumbs-up nav-icon"></i>
                        <span class="nav-text">Document Recommendations</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="progress">
                        <i class="fas fa-chart-line nav-icon"></i>
                        <span class="nav-text">Progress Monitoring</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="reports">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        <span class="nav-text">Reports Access</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="notifications">
                        <i class="fas fa-bell nav-icon"></i>
                        <span class="nav-text">Notifications</span>
                    </a>
                </div>
                <hr class="my-3 mx-3" style="border-color: rgba(255,255,255,0.2);">
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </div>
        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="d-flex align-items-center">
                    <button class="sidebar-toggle me-3" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="sidebar-toggle d-lg-none" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title mb-0" id="pageTitle">Dashboard</h1>
                </div>
                <div class="top-nav-actions">
                    <div class="notification-badge me-3">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="badge">4</span>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <span id="topUserName">subareachair</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- Content Area -->
            <div class="content-area" id="contentArea">
                <!-- Dashboard content will be loaded here -->
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- <script src="../js/auth.js"></script> -->
    <!-- <script src="../js/utils.js"></script> -->
    <!-- <script src="../js/dashboard.js"></script> -->
    <script>
        class SubAreaChairDashboard extends ADAMSDashboard {
            constructor() {
                super();
                this.setupSubAreaChairFeatures();
            }
            setupSubAreaChairFeatures() {
                this.loadUserData();
            }
            loadUserData() {
                const user = ADAMSAuth.getCurrentUser();
                if (user) {
                    document.getElementById('userName').textContent = user.username;
                    document.getElementById('topUserName').textContent = user.username;
                }
            }
            renderRecommendations(container) {
                const docs = [
                    { id: 1, title: 'Research Output 2024', type: 'Research', submittedBy: 'Prof. A. Cruz', submittedAt: new Date(Date.now() - 86400000), status: 'pending', fileName: 'research_output_2024.pdf' },
                    { id: 2, title: 'Student Records Q1', type: 'Student Records', submittedBy: 'Registrar', submittedAt: new Date(Date.now() - 172800000), status: 'pending', fileName: 'student_records_q1.pdf' },
                    { id: 3, title: 'Extension Program Report', type: 'Extension', submittedBy: 'Engr. B. Reyes', submittedAt: new Date(Date.now() - 259200000), status: 'reviewed', fileName: 'extension_report.pdf' }
                ];
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Document Recommendations</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">Documents for Recommendation</h5></div><div class="card-body"><div class="table-responsive"><table class="table table-hover"><thead><tr><th>Document</th><th>Type</th><th>Submitted By</th><th>Submitted</th><th>Status</th><th>Actions</th></tr></thead><tbody>
                    ${docs.map(doc => `
                        <tr>
                            <td><i class="fas ${ADAMSUtils.getFileIcon(doc.fileName)} fa-2x text-muted me-2"></i>${doc.title}</td>
                            <td>${doc.type}</td>
                            <td>${doc.submittedBy}</td>
                            <td>${ADAMSUtils.timeAgo(doc.submittedAt)}</td>
                            <td>${ADAMSUtils.getStatusBadge(doc.status)}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary btn-view" title="View"><i class="fas fa-eye"></i></button>
                                    <button class="btn btn-outline-success btn-recommend" title="Recommend" ${doc.status === 'reviewed' ? 'disabled' : ''}><i class="fas fa-thumbs-up"></i></button>
                                    <button class="btn btn-outline-info btn-comment" title="Comment"><i class="fas fa-comment"></i></button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                    </tbody></table></div></div></div>
                `;
                this.setupRecommendationEventListeners();
            }
            renderProgressTracking(container) {
                const progress = 85;
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Progress Monitoring</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">Document Submission & Review Progress</h5></div><div class="card-body">
                        <div class="mb-4"><div class="progress" style="height: 24px;"><div class="progress-bar bg-success" style="width: ${progress}%">${progress}% Complete</div></div></div>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">Research Output <span class="badge bg-success">Reviewed</span></li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">Student Records <span class="badge bg-warning">Pending</span></li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">Extension Program <span class="badge bg-info">In Progress</span></li>
                        </ul>
                    </div></div>
                `;
            }
            renderReports(container) {
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Reports Access</h2></div></div>
                    <div class="row"><div class="col-12"><div class="card"><div class="card-header"><h5 class="mb-0">Authorized Reports</h5></div><div class="card-body">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">Research Summary <button class="btn btn-outline-primary btn-sm">View</button></li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">Student Records Report <button class="btn btn-outline-primary btn-sm">View</button></li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">Extension Program Analytics <button class="btn btn-outline-primary btn-sm">View</button></li>
                        </ul>
                    </div></div></div></div>
                `;
            }
            renderNotifications(container) {
                const notifications = [
                    { id: 1, title: 'New Document Assigned', message: 'Research Output 2024 assigned for recommendation', time: '10 min ago', type: 'info', read: false },
                    { id: 2, title: 'Deadline Reminder', message: 'Student Records review due in 1 day', time: '1 hour ago', type: 'warning', read: false },
                    { id: 3, title: 'Comment Added', message: 'Area Chair commented on Extension Program', time: '2 hours ago', type: 'info', read: true },
                    { id: 4, title: 'System Update', message: 'New analytics report available', time: '1 day ago', type: 'success', read: true }
                ];
                container.innerHTML = `
                    <div class="row mb-4"><div class="col-12"><h2>Notifications</h2></div></div>
                    <div class="card"><div class="card-header"><h5 class="mb-0">Recent Notifications</h5></div><div class="card-body">
                        <div class="notification-list">
                            ${notifications.map(n => `
                                <div class="notification-item d-flex align-items-start p-3 border-bottom ${!n.read ? 'bg-light' : ''}">
                                    <div class="notification-icon me-3"><i class="fas fa-${this.getNotificationIcon(n.type)} text-${n.type}"></i></div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1 ${!n.read ? 'fw-bold' : ''}">${n.title}</h6>
                                                <p class="text-muted mb-1">${n.message}</p>
                                                <small class="text-muted">${n.time}</small>
                                            </div>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm">View</button>
                                                <button class="btn btn-outline-secondary btn-sm">Mark Read</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div></div>
                `;
            }
            setupRecommendationEventListeners() {
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.btn-recommend')) {
                        this.handleRecommend(e);
                    }
                    if (e.target.closest('.btn-comment')) {
                        this.handleComment(e);
                    }
                });
            }
            handleRecommend(e) {
                e.preventDefault();
                ADAMSUtils.showToast('Document recommended for approval', 'success');
                const row = e.target.closest('tr');
                const statusCell = row.querySelector('td:nth-child(5)');
                statusCell.innerHTML = ADAMSUtils.getStatusBadge('reviewed');
                e.target.closest('.btn-recommend').disabled = true;
            }
            handleComment(e) {
                e.preventDefault();
                const comment = prompt('Add your comment:');
                if (comment) {
                    ADAMSUtils.showToast('Comment added successfully', 'info');
                }
            }
            getNotificationIcon(type) {
                const icons = { info: 'info-circle', warning: 'exclamation-triangle', success: 'check-circle', danger: 'times-circle' };
                return icons[type] || 'bell';
            }
        }
        document.addEventListener('DOMContentLoaded', () => {
            new SubAreaChairDashboard();
        });
    </script>
</body>
</html> 