// ADAMS Dashboard System
class ADAMSDashboard {
    constructor() {
        // Temporarily disable auth check for testing
        // this.currentUser = ADAMSAuth.getCurrentUser();
        this.currentUser = { role: 'admin', username: 'admin', name: 'System Administrator' };
        this.currentPage = 'dashboard';
        this.init();
    }
    
    init() {
        // Temporarily disable auth check for testing
        // if (!ADAMSAuth.checkAuth()) return;
        
        this.setupSidebar();
        this.setupNavigation();
        this.loadDashboardContent();
        this.setupEventListeners();
    }
    
    setupSidebar() {
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });
        }
        
        // Mobile sidebar toggle
        const mobileToggle = document.getElementById('mobileSidebarToggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    }
    
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.navigateToPage(page);
            });
        });
        
        // Set active nav link
        this.setActiveNavLink();
    }
    
    navigateToPage(page) {
        this.currentPage = page;
        this.setActiveNavLink();
        this.loadPageContent(page);
        this.updatePageTitle(page);
    }
    
    setActiveNavLink() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === this.currentPage) {
                link.classList.add('active');
            }
        });
    }
    
    updatePageTitle(page) {
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            const titles = {
                dashboard: 'Dashboard',
                users: 'User Management',
                config: 'System Configuration',
                scheduling: 'Accreditation Scheduling',
                documents: 'Document Management',
                logs: 'Audit Logs',
                reports: 'Reports & Analytics',
                announcements: 'Announcements',
                settings: 'Settings',
                review: 'Document Review',
                progress: 'Progress Tracking',
                recommendations: 'Document Recommendations',
                upload: 'Document Upload',
                tracking: 'Status Tracking',
                requests: 'Access Requests',
                calendar: 'Deadline Calendar'
            };
            pageTitle.textContent = titles[page] || 'Dashboard';
        }
    }
    
    loadPageContent(page) {
        const contentArea = document.getElementById('contentArea');
        if (!contentArea) return;
        
        // Show loading state
        contentArea.innerHTML = '<div class="text-center py-5"><div class="spinner"></div><p class="mt-3">Loading...</p></div>';
        
        // Load content based on page and user role
        setTimeout(() => {
            this.renderPageContent(page, contentArea);
        }, 500);
    }
    
    renderPageContent(page, container) {
        const role = this.currentUser.role;
        
        switch (page) {
            case 'dashboard':
                this.renderDashboard(container, role);
                break;
            case 'users':
                if (role === 'admin') this.renderUserManagement(container);
                break;
            case 'config':
                if (role === 'admin') this.renderSystemConfig(container);
                break;
            case 'scheduling':
                if (role === 'admin') this.renderScheduling(container);
                break;
            case 'documents':
                this.renderDocumentManagement(container, role);
                break;
            case 'logs':
                if (role === 'admin') this.renderAuditLogs(container);
                break;
            case 'reports':
                this.renderReports(container, role);
                break;
            case 'announcements':
                if (role === 'admin') this.renderAnnouncements(container);
                break;
            case 'settings':
                if (role === 'admin') this.renderSettings(container);
                break;
            case 'review':
                if (role === 'areachair') this.renderDocumentReview(container);
                break;
            case 'progress':
                if (role === 'areachair' || role === 'subareachair') this.renderProgressTracking(container, role);
                break;
            case 'recommendations':
                if (role === 'subareachair') this.renderRecommendations(container);
                break;
            case 'upload':
                if (role === 'member') this.renderDocumentUpload(container);
                break;
            case 'tracking':
                if (role === 'member') this.renderStatusTracking(container);
                break;
            case 'requests':
                if (role === 'member') this.renderAccessRequests(container);
                break;
            case 'calendar':
                if (role === 'member') this.renderCalendar(container);
                break;
            default:
                this.renderDashboard(container, role);
        }
    }
    
    renderDashboard(container, role) {
        const dashboardData = this.getDashboardData(role);
        
        container.innerHTML = `
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="mb-3">Welcome back, ${this.currentUser.name}!</h2>
                    <p class="text-muted">Here's what's happening with your accreditation activities.</p>
                </div>
            </div>
            
            <!-- Metrics Cards -->
            <div class="row mb-4">
                ${dashboardData.metrics.map(metric => `
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-value">${metric.value}</div>
                            <div class="metric-label">${metric.label}</div>
                            <i class="fas ${metric.icon} metric-icon"></i>
                        </div>
                    </div>
                `).join('')}
            </div>
            
            <!-- Recent Activity -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="activity-list">
                                ${dashboardData.recentActivity.map(activity => `
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="activity-icon me-3">
                                            <i class="fas ${activity.icon} text-${activity.color}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="fw-semibold">${activity.title}</div>
                                            <small class="text-muted">${activity.time}</small>
                                        </div>
                                        <span class="badge bg-${activity.status}">${activity.statusText}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notifications</h5>
                        </div>
                        <div class="card-body">
                            <div class="notification-list">
                                ${dashboardData.notifications.map(notification => `
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="notification-icon me-2">
                                            <i class="fas ${notification.icon} text-${notification.color}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="small fw-semibold">${notification.title}</div>
                                            <small class="text-muted">${notification.message}</small>
                                            <div class="small text-muted mt-1">${notification.time}</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    getDashboardData(role) {
        const data = {
            admin: {
                metrics: [
                    { value: '156', label: 'Total Users', icon: 'fa-users' },
                    { value: '89', label: 'Active Documents', icon: 'fa-file-alt' },
                    { value: '23', label: 'Pending Approvals', icon: 'fa-clock' },
                    { value: '95%', label: 'System Uptime', icon: 'fa-server' }
                ],
                recentActivity: [
                    { title: 'New user registered', time: '2 minutes ago', icon: 'fa-user-plus', color: 'success', status: 'success', statusText: 'Completed' },
                    { title: 'Document approved', time: '15 minutes ago', icon: 'fa-check-circle', color: 'success', status: 'success', statusText: 'Approved' },
                    { title: 'System backup completed', time: '1 hour ago', icon: 'fa-database', color: 'info', status: 'info', statusText: 'Completed' },
                    { title: 'New announcement posted', time: '2 hours ago', icon: 'fa-bullhorn', color: 'warning', status: 'warning', statusText: 'Posted' }
                ],
                notifications: [
                    { title: 'System Update', message: 'New features available', time: '5 min ago', icon: 'fa-cog', color: 'info' },
                    { title: 'Security Alert', message: 'Login attempt detected', time: '1 hour ago', icon: 'fa-shield-alt', color: 'warning' },
                    { title: 'Backup Complete', message: 'Daily backup successful', time: '2 hours ago', icon: 'fa-check', color: 'success' }
                ]
            },
            areachair: {
                metrics: [
                    { value: '45', label: 'Documents to Review', icon: 'fa-file-alt' },
                    { value: '12', label: 'Pending Approvals', icon: 'fa-clock' },
                    { value: '78%', label: 'Completion Rate', icon: 'fa-chart-pie' },
                    { value: '8', label: 'Team Members', icon: 'fa-users' }
                ],
                recentActivity: [
                    { title: 'Document submitted for review', time: '5 minutes ago', icon: 'fa-file-upload', color: 'info', status: 'warning', statusText: 'Pending' },
                    { title: 'Review completed', time: '1 hour ago', icon: 'fa-check', color: 'success', status: 'success', statusText: 'Approved' },
                    { title: 'Comment added', time: '2 hours ago', icon: 'fa-comment', color: 'info', status: 'info', statusText: 'Updated' }
                ],
                notifications: [
                    { title: 'New Document', message: 'BSIT Curriculum submitted', time: '5 min ago', icon: 'fa-file', color: 'info' },
                    { title: 'Review Due', message: 'Faculty credentials due tomorrow', time: '1 hour ago', icon: 'fa-clock', color: 'warning' }
                ]
            },
            subareachair: {
                metrics: [
                    { value: '23', label: 'Documents to Review', icon: 'fa-file-alt' },
                    { value: '7', label: 'Recommendations', icon: 'fa-thumbs-up' },
                    { value: '85%', label: 'Progress Rate', icon: 'fa-chart-line' },
                    { value: '5', label: 'Team Members', icon: 'fa-users' }
                ],
                recentActivity: [
                    { title: 'Document reviewed', time: '10 minutes ago', icon: 'fa-eye', color: 'info', status: 'info', statusText: 'Reviewed' },
                    { title: 'Recommendation sent', time: '1 hour ago', icon: 'fa-paper-plane', color: 'success', status: 'success', statusText: 'Sent' },
                    { title: 'Comment added', time: '2 hours ago', icon: 'fa-comment', color: 'info', status: 'info', statusText: 'Updated' }
                ],
                notifications: [
                    { title: 'Review Request', message: 'New document needs review', time: '10 min ago', icon: 'fa-file', color: 'info' },
                    { title: 'Deadline Reminder', message: 'Review due in 2 days', time: '1 hour ago', icon: 'fa-clock', color: 'warning' }
                ]
            },
            member: {
                metrics: [
                    { value: '15', label: 'Documents Submitted', icon: 'fa-file-alt' },
                    { value: '8', label: 'Pending Reviews', icon: 'fa-clock' },
                    { value: '92%', label: 'Approval Rate', icon: 'fa-chart-pie' },
                    { value: '3', label: 'Days Remaining', icon: 'fa-calendar' }
                ],
                recentActivity: [
                    { title: 'Document uploaded', time: '30 minutes ago', icon: 'fa-upload', color: 'success', status: 'success', statusText: 'Uploaded' },
                    { title: 'Document approved', time: '2 hours ago', icon: 'fa-check-circle', color: 'success', status: 'success', statusText: 'Approved' },
                    { title: 'Comment received', time: '1 day ago', icon: 'fa-comment', color: 'info', status: 'info', statusText: 'Updated' }
                ],
                notifications: [
                    { title: 'Document Approved', message: 'Faculty credentials approved', time: '2 hours ago', icon: 'fa-check', color: 'success' },
                    { title: 'Deadline Reminder', message: 'Submission due in 3 days', time: '1 day ago', icon: 'fa-clock', color: 'warning' }
                ]
            }
        };
        
        return data[role] || data.member;
    }
    
    setupEventListeners() {
        // Global event listeners for dashboard functionality
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-delete')) {
                this.handleDelete(e);
            }
            
            if (e.target.classList.contains('btn-edit')) {
                this.handleEdit(e);
            }
            
            if (e.target.classList.contains('btn-view')) {
                this.handleView(e);
            }
        });
    }
    
    handleDelete(e) {
        e.preventDefault();
        if (confirm('Are you sure you want to delete this item?')) {
            // Handle delete logic
            this.showNotification('Item deleted successfully', 'success');
        }
    }
    
    handleEdit(e) {
        e.preventDefault();
        // Handle edit logic
        this.showNotification('Edit mode activated', 'info');
    }
    
    handleView(e) {
        e.preventDefault();
        // Handle view logic
        this.showNotification('Viewing details', 'info');
    }
    
    showNotification(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
    
    loadDashboardContent() {
        // Load initial dashboard content
        this.renderPageContent(this.currentPage, document.getElementById('contentArea'));
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ADAMSDashboard();
}); 